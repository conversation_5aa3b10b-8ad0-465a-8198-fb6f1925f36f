<template>
    <view class="container">
        <!-- 内容列表 -->
        <view class="feed-list">
            <!-- 内容卡片 -->
            <view class="feed-card" v-for="(item, dataListindex) in new_list" :key="item.id">
                <!-- 置顶标识 -->
                <image
                    v-if="item.top_time"
                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/top1.png'"
                    style="opacity: 0.8; width: 70px; height: 70px; position: absolute; right: 0; top: 2%; z-index: 200"
                ></image>

                <!-- 顶部装饰条 - 不同内容类型显示不同颜色 -->
                <view class="feed-type-indicator" :class="{
                    'image': item.study_type === 0 || item.study_type === 6,
                    'audio': item.study_type === 1,
                    'video': item.study_type === 2,
                    'event': item.study_type === 3,
                    'poll': item.study_type === 4 || item.study_type === 5
                }"></view>

                <!-- 用户信息 -->
                <view class="user-info">
                    <view
                        @tap="home_url"
                        :data-index="dataListindex"
                        data-k="1"
                        :data-user_id="item.user_id"
                        class="avatar-container"
                    >
                        <image class="avatar" :src="item.user_head_sculpture" mode="aspectFill"></image>
                        <image
                            v-if="item.user_id != 0"
                            class="avatar-frame"
                            :src="item.avatar_frame"
                        ></image>
                    </view>
                    <view class="user-details">
                        <view class="username-row">
                            <text class="username" :class="item.user_id != 0 ? item.special : ''">{{ item.user_nick_name }}</text>
                            <image v-if="item.attr && item.attr.attest" class="user-badge-img" :src="item.attr.attest.at_icon"></image>
                            <image
                                v-if="item.user_vip == 1 && item.user_id != 0"
                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                class="vip-badge"
                            ></image>
                            <image
                                v-if="item.user_id != 0"
                                mode="heightFix"
                                :src="item.level"
                                class="level-badge"
                            ></image>
                            <image
                                v-if="item.wear_merit && item.user_id != 0"
                                mode="heightFix"
                                :src="item.wear_merit"
                                class="merit-badge"
                            ></image>
                        </view>
                        <view class="post-time">
                            <text
                                v-if="item.check_qq == 'da' && item.user_id != 0"
                                class="admin-badge main-admin"
                            >
                                {{ $state.diy && $state.diy.qq_name ? $state.diy.qq_name : 'QQ' }}主
                            </text>
                            <text
                                v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                class="admin-badge sub-admin"
                            >
                                管理
                            </text>
                            <text @tap="home_url" :data-id="item.tory_id" data-k="2" class="realm-info">
                                发布于 {{ item.realm_name }}
                            </text>
                            <text class="time-info">{{ item.adapter_time }}</text>
                        </view>
                    </view>
                    <view class="action-badges">
                        <image
                            v-if="item.red == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/fl.png'"
                            class="action-badge"
                        ></image>
                        <image
                            v-if="item.study_type == 3"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/hd.png'"
                            class="action-badge"
                        ></image>
                        <image
                            v-if="item.is_buy == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index3/ff.png'"
                            class="action-badge"
                        ></image>
                    </view>
                </view>

                <!-- 内容区域 - 根据类型展示不同内容 -->
                <view class="content">
                    <!-- 文本内容 -->
                    <view
                        class="content-text"
                        v-if="item.study_type == 0 || item.study_type == 1 || item.study_type == 2 || item.study_type == 3 || item.study_type == 6"
                        @tap="home_url"
                        :data-index="dataListindex"
                        data-k="3"
                        :data-type="item.study_type"
                        :data-id="item.id"
                    >
                        <view class="title-text" v-if="item.study_title" :style="'color:' + (item.study_title_color || '#333') + ';'">
                            <rich-text :nodes="item.study_title"></rich-text>
                        </view>
                        <view class="main-text" v-if="item.study_content">
                            <rich-text :nodes="item.study_content"></rich-text>
                        </view>
                    </view>

                    <!-- 图文内容 -->
                    <view
                        class="image-grid"
                        v-if="(item.study_type == 0 || item.study_type == 3) && item.image_part && item.image_part.length > 0"
                        @tap="home_url"
                        :data-index="dataListindex"
                        data-k="3"
                        :data-type="item.study_type"
                        :data-id="item.id"
                    >
                        <!-- 单张图片 -->
                        <view v-if="item.image_part.length == 1" class="grid-container grid-single">
                            <view class="grid-item">
                                <image :lazy-load="true" :src="item.image_part[0]" mode="aspectFill" class="grid-img"></image>
                            </view>
                        </view>

                        <!-- 两张图片 -->
                        <view v-if="item.image_part.length == 2" class="grid-container grid-double">
                            <view class="grid-item" v-for="(img, i) in item.image_part" :key="i">
                                <image :lazy-load="true" :src="img" mode="aspectFill" class="grid-img"></image>
                            </view>
                        </view>

                        <!-- 多张图片 -->
                        <view v-if="item.image_part.length > 2" class="grid-container grid-multiple">
                            <view class="grid-item" v-for="(img, i) in item.image_part.slice(0, 3)" :key="i">
                                <image :lazy-load="true" :src="img" mode="aspectFill" class="grid-img"></image>
                                <view class="img-count" v-if="item.image_part.length > 3 && i === 2">
                                    +{{ item.image_part.length - 3 }}
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 语音内容 -->
                    <view class="audio-content" v-if="item.study_type === 1">
                        <view class="audio-player-container">
                            <view class="audio-cover-section" :style="'background-image: url(' + item.user_head_sculpture + ');'">
                                <view class="audio-control" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text class="play-icon">▶</text>
                                </view>
                                <view class="audio-control" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text class="pause-icon">⏸</text>
                                </view>
                            </view>
                            <view class="audio-info">
                                <view class="audio-title">{{ item.user_nick_name }}上传的音乐</view>
                                <view class="audio-time">{{ item.starttime }}</view>
                                <view class="audio-meta">
                                    <view class="audio-artist">{{ item.user_nick_name }}</view>
                                    <slider
                                        class="audio-slider"
                                        @change="sliderChange"
                                        block-size="12px"
                                        step="1"
                                        :value="item.offset || 0"
                                        :max="item.max || 100"
                                        selected-color="#7B61FF"
                                    />
                                </view>
                            </view>
                        </view>

                        <!-- 音频相关图片 -->
                        <view
                            class="audio-images"
                            v-if="item.image_part && item.image_part.length > 0"
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                        >
                            <!-- 单张图片 -->
                            <view v-if="item.image_part.length == 1" class="grid-container grid-single">
                                <view class="grid-item">
                                    <image :lazy-load="true" :src="item.image_part[0]" mode="aspectFill" class="grid-img"></image>
                                </view>
                            </view>

                            <!-- 两张图片 -->
                            <view v-if="item.image_part.length == 2" class="grid-container grid-double">
                                <view class="grid-item" v-for="(img, i) in item.image_part" :key="i">
                                    <image :lazy-load="true" :src="img" mode="aspectFill" class="grid-img"></image>
                                </view>
                            </view>

                            <!-- 多张图片 -->
                            <view v-if="item.image_part.length > 2" class="grid-container grid-multiple">
                                <view class="grid-item" v-for="(img, i) in item.image_part.slice(0, 3)" :key="i">
                                    <image :lazy-load="true" :src="img" mode="aspectFill" class="grid-img"></image>
                                    <view class="img-count" v-if="item.image_part.length > 3 && i === 2">
                                        +{{ item.image_part.length - 3 }}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 视频内容 -->
                    <view
                        class="video-content"
                        v-if="item.study_type === 2"
                        @tap="home_url"
                        :data-index="dataListindex"
                        data-k="3"
                        :data-type="item.study_type"
                        :data-id="item.id"
                    >
                        <view class="video-container">
                            <view v-if="item.image_part && item.image_part.length > 0" class="video-poster">
                                <image :src="item.image_part[0]" mode="aspectFill" class="video-cover"></image>
                                <view class="video-play-btn">
                                    <text class="video-play-icon">▶</text>
                                </view>
                            </view>
                            <view v-else class="video-placeholder">
                                <view class="video-play-btn">
                                    <text class="video-play-icon">▶</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 话题标签 -->
                    <view
                        v-if="item.gambit_id"
                        @tap="gambit_list"
                        :data-id="item.gambit_id"
                        class="topic-tag"
                    >
                        <image
                            class="topic-icon"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"
                        ></image>
                        <text class="topic-text">{{ item.gambit_name }}</text>
                    </view>

                    <!-- 投票内容 -->
                    <view class="poll-content" v-if="item.study_type == 4 || item.study_type == 5">
                        <view class="poll-header">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                class="poll-question"
                            >
                                <text v-if="item.study_type == 4">（单选）</text>
                                <text v-if="item.study_type == 5">（多选）</text>
                                <rich-text v-if="item.study_title" :nodes="item.study_title"></rich-text>
                            </view>
                        </view>

                        <view class="poll-options">
                            <view
                                class="poll-option"
                                v-for="(vo_item, vo_index) in item.vo"
                                :key="vo_item.id"
                                v-if="vo_index < 3"
                                @tap="dian_option"
                                :data-id="vo_item.id"
                                :data-key="dataListindex"
                                :data-index="vo_index"
                                :class="{ selected: item.vo_id && item.vo_id.includes(vo_item.id) }"
                            >
                                <view class="option-text">{{ vo_item.ballot_name }}</view>
                                <view class="option-stats" v-if="item.is_vo_check > 0">
                                    <view class="progress-bar">
                                        <view class="progress-fill" :style="{ width: vo_item.ratio + '%', backgroundColor: getProgressColor(item.id) }"></view>
                                    </view>
                                    <view class="percentage">{{ vo_item.voters }}</view>
                                </view>
                                <view class="option-check" v-if="item.vo_id && item.vo_id.includes(vo_item.id)">
                                    <text class="check-icon">✓</text>
                                </view>
                            </view>

                            <view
                                v-if="item.vo && item.vo.length > 3"
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                class="poll-option view-more"
                            >
                                查看全部选项
                                <text class="arrow-right">→</text>
                            </view>
                        </view>

                        <view class="poll-footer">
                            <view class="poll-stats">参与人数：{{ item.vo_count || 0 }}</view>
                            <button
                                v-if="item.vo_id && item.vo_id.length > 0 && item.is_vo_check == 0"
                                @tap="vote_do"
                                :data-index="vo_index"
                                :data-key="dataListindex"
                                class="vote-btn"
                            >
                                投票
                            </button>
                        </view>
                    </view>
                </view>

                <!-- 互动区域 -->
                <view class="actions">
                    <view class="action-item" @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')" :data-id="item.id" :data-key="dataListindex">
                        <text v-if="!item.is_info_zan" class="action-icon like-icon">👍</text>
                        <text v-if="item.is_info_zan" class="action-icon like-icon liked">👍</text>
                        <text class="action-text">{{ formatNumber(item.info_zan_count_this || 0) }}</text>
                    </view>
                    <view class="action-item" @tap="home_pl" :data-id="item.id" :data-key="dataListindex">
                        <text class="action-icon comment-icon">💬</text>
                        <text class="action-text">{{ formatNumber(item.study_repount || 0) }}</text>
                    </view>
                    <view class="action-item">
                        <text class="action-icon share-icon">📤</text>
                        <text class="action-text">分享</text>
                    </view>
                    <view class="action-item bookmark" @tap="toggleBookmark(item.id)">
                        <text class="action-icon bookmark-icon" :class="{ bookmarked: item.bookmarked }">⭐</text>
                    </view>
                </view>

                <!-- 评论预览 -->
                <view
                    class="comments-preview"
                    v-if="item.study_repount > 0 && item.reply_list && item.reply_list.length"
                    @tap="home_url"
                    :data-index="dataListindex"
                    data-k="3"
                    :data-type="item.study_type"
                    :data-id="item.id"
                >
                    <view class="comments-header">共{{ item.study_repount }}条评论</view>
                    <view class="comment-item" v-for="(r, index) in item.reply_list" :key="index">
                        <text class="comment-username">{{ r.user_nick_name }}：</text>
                        <rich-text class="comment-content" :nodes="r.reply_content"></rich-text>
                        <image
                            v-if="r.image_part && r.image_part[0]"
                            @tap.stop="previewHuiAndImage"
                            :src="r.image_part[0]"
                            :data-src="r.image_part[0]"
                            class="comment-image"
                        ></image>
                    </view>
                    <view class="view-more-comments">进入查看更多评论</view>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 广告位 -->
            <view v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                <ad :unit-id="ad_info.adunit_id"></ad>
            </view>
        </block>

        <!-- 加载状态 -->
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>

        <!-- 下拉刷新提示 -->
        <view class="refresh-hint" v-if="refreshing">
            <view class="spinner"></view>
            <text>刷新中...</text>
        </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    data() {
        return {
            refreshing: false
        };
    },
    computed: {
        // 从父组件获取数据列表
        new_list() {
            return this.$parent.$data.new_list || [];
        },
        http_root() {
            return this.$parent.$data.http_root || '';
        },
        $state() {
            return this.$parent.$data.$state || {};
        },
        version() {
            return this.$parent.$data.version || 0;
        },
        ad_info() {
            return this.$parent.$data.ad_info || {};
        },
        di_msg() {
            return this.$parent.$data.di_msg || false;
        },
        // 计算每个图文内容的网格布局类名，存储在对象中
        gridClassMap() {
            const map = {};
            this.new_list.forEach(item => {
                if ((item.study_type === 0 || item.study_type === 3) && item.image_part) {
                    const count = item.image_part.length;
                    if (count === 1) {
                        map[item.id] = 'grid-single';
                    } else if (count === 2) {
                        map[item.id] = 'grid-double';
                    } else {
                        map[item.id] = 'grid-multiple';
                    }
                }
            });
            return map;
        }
    },
    methods: {
        // 来自 index9.vue 的事件处理方法
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        },
        previewHuiAndImage(e) {
            this.$emit('preview-image', e);
        },

        // 点赞/取消点赞 - 适配新数据结构
        toggleLike(id) {
            const item = this.new_list.find(item => item.id === id);
            if (item) {
                if (item.is_info_zan) {
                    item.info_zan_count_this--;
                } else {
                    item.info_zan_count_this++;
                }
                item.is_info_zan = !item.is_info_zan;
            }
        },

        // 收藏/取消收藏
        toggleBookmark(id) {
            const item = this.new_list.find(item => item.id === id);
            if (item) {
                item.bookmarked = !item.bookmarked;
            }
        },

        // 选择投票选项
        selectOption(id, optionIndex) {
            const item = this.new_list.find(item => item.id === id);
            if (item && item.vo) {
                // 处理投票选项选择逻辑
                if (!item.vo_id) {
                    item.vo_id = [];
                }
                const optionId = item.vo[optionIndex].id;
                const index = item.vo_id.indexOf(optionId);

                if (item.study_type === 4) { // 单选
                    item.vo_id = index > -1 ? [] : [optionId];
                } else if (item.study_type === 5) { // 多选
                    if (index > -1) {
                        item.vo_id.splice(index, 1);
                    } else {
                        item.vo_id.push(optionId);
                    }
                }
            }
        },

        // 处理视频播放
        handleVideoPlay(id) {
            // 暂停其他正在播放的视频
            this.new_list.forEach(item => {
                if (item.id !== id && item.study_type === 2) {
                    // 实际项目中可以通过video上下文暂停视频
                }
            });
        },

        // 处理视频暂停
        handleVideoPause(id) {
            // 视频暂停处理
        },

        // 下拉刷新
        onRefresh() {
            this.refreshing = true;
            // 模拟刷新延迟
            setTimeout(() => {
                this.refreshing = false;
                // 实际项目中可以在这里重新请求数据
            }, 1000);
        },

        // 格式化数字（大于1000显示k）
        formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + 'w';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num;
        },

        // 获取项目主题色
        getThemeColor(id) {
            const item = this.new_list.find(item => item.id === id);
            if (item) {
                switch(item.study_type) {
                    case 0: return '#FF4D6D'; // 图文
                    case 1: return '#7B61FF'; // 音频
                    case 2: return '#00C853'; // 视频
                    case 3: return '#FF9800'; // 活动
                    case 4:
                    case 5: return '#2196F3'; // 投票
                    default: return '#FF4D6D';
                }
            }
            return '#FF4D6D';
        },

        // 获取进度条颜色
        getProgressColor(id) {
            return this.getThemeColor(id);
        }
    }
};
</script>

<style scoped>
/* 样式保持不变，与上一版本相同 */
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 内容列表 */
.feed-list {
    flex: 1;
    padding: 12px 0;
}

/* 内容卡片 */
.feed-card {
    background-color: white;
    border-radius: 12px;
    margin: 0 16px 12px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.feed-card:active {
    transform: scale(0.995);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
}

/* 内容类型指示条 */
.feed-type-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    border-radius: 2px 0 0 2px;
}

.feed-type-indicator.image {
    background-color: #FF4D6D;
}

.feed-type-indicator.audio {
    background-color: #7B61FF;
}

.feed-type-indicator.video {
    background-color: #00C853;
}

.feed-type-indicator.event {
    background-color: #FF9800;
}

.feed-type-indicator.poll {
    background-color: #2196F3;
}

/* 用户信息样式 */
.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.avatar-container {
    position: relative;
    margin-right: 10px;
}

.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 1px solid #f0f0f0;
}

.avatar-frame {
    position: absolute;
    top: -6px;
    left: -6px;
    width: 54px;
    height: 54px;
    max-width: initial;
}

.user-badge-img {
    width: 18px;
    height: 18px;
    margin-left: 3px;
}

.vip-badge {
    width: 15px;
    height: 15px;
    vertical-align: bottom;
    margin-left: 3px;
}

.level-badge {
    height: 13px;
    vertical-align: middle;
    margin-left: 3px;
}

.merit-badge {
    height: 13px;
    margin-left: 3px;
}

.admin-badge {
    font-size: 10px;
    padding: 0px 4px;
    border-radius: 2px;
    margin-right: 5px;
}

.main-admin {
    background-color: #9966ff;
    color: #fff;
}

.sub-admin {
    background-color: #4facfe;
    color: #fff;
}

.realm-info {
    font-size: 12px;
    color: #777;
}

.time-info {
    font-size: 12px;
    color: #666;
    margin-left: 8px;
}

.action-badges {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.action-badge {
    width: 30px;
    height: 15px;
}

.user-details {
    flex: 1;
}

.username-row {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.username {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-right: 6px;
}

.user-badge {
    font-size: 11px;
    background-color: #f0f0f0;
    color: #666;
    padding: 1px 6px;
    border-radius: 10px;
}

.post-time {
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
}

.visibility {
    font-size: 11px;
}

.follow-btn {
    background-color: #FF4D6D;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
    transition: background-color 0.2s;
}

.follow-btn:active {
    background-color: #e0435f;
}

.followed-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 3px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
}

/* 内容区域 */
.content {
    margin-bottom: 16px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.content-text {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 12px;
    white-space: pre-line;
}

.title-text {
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 8px;
}

.main-text {
    font-size: 13px;
    margin-top: 8px;
}

/* 话题标签样式 */
.topic-tag {
    display: inline-block;
    background-color: #ededed;
    border-radius: 20px;
    padding: 2px 5px 2px 2px;
    font-size: 12px;
    margin-top: 15px;
    font-weight: 300;
}

.topic-icon {
    width: 15px;
    height: 15px;
    vertical-align: middle;
}

.topic-text {
    vertical-align: middle;
    margin-left: 5px;
    letter-spacing: 1px;
}

/* 话题标签 */
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.tag {
    font-size: 13px;
    color: #6B7280;
    background-color: #F3F4F6;
    padding: 2px 8px;
    border-radius: 4px;
}

/* 图文内容 */
.image-grid {
    margin-bottom: 12px;
}

.grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.grid-single {
    aspect-ratio: 16/9;
}

.grid-single .grid-item {
    width: 100%;
    height: 100%;
}

.grid-double .grid-item {
    width: calc(50% - 2px);
    aspect-ratio: 1;
}

.grid-multiple .grid-item {
    width: calc(33.333% - 2.66px);
    aspect-ratio: 1;
}

.grid-item {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.grid-img {
    width: 100%;
    height: 100%;
    display: block;
}

.img-count {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px 0 0 0;
}

/* 语音内容 */
.audio-content {
    margin-bottom: 12px;
}

.audio-player-container {
    display: flex;
    align-items: center;
    background-color: #f6f7f7;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
    height: 85px;
    overflow: hidden;
}

.audio-cover-section {
    width: 85px;
    height: 85px;
    background-size: cover;
    background-position: center;
    background-color: #000;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-control {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.play-icon, .pause-icon {
    color: #ffffff;
    font-size: 15px;
}

.audio-info {
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.audio-title {
    font-size: 14px;
    color: #555555;
    font-weight: 600;
}

.audio-time {
    font-size: 12px;
    color: #999;
}

.audio-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.audio-artist {
    font-size: 12px;
    color: #999;
}

.audio-slider {
    width: 85px;
}

.audio-images {
    margin-top: 12px;
    padding: 0px 10px;
}

/* 视频内容 */
.video-content {
    margin-bottom: 12px;
}

.video-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background-color: #000;
}

.video-poster {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
}

.video-cover {
    width: 100%;
    height: 100%;
    display: block;
}

.video-placeholder {
    width: 80%;
    height: 180px;
    margin: 0 auto;
    background-color: #000;
    border-radius: 8px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.video-play-icon {
    color: white;
    font-size: 24px;
}

/* 活动内容 */
.event-content {
    margin-bottom: 12px;
}

.event-banner-container {
    position: relative;
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
}

.event-banner {
    width: 100%;
    aspect-ratio: 2/1;
    display: block;
}

.event-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #FF9800;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
}

.event-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.event-date,
.event-location {
    display: flex;
    align-items: center;
    gap: 4px;
}

.event-participants {
    display: flex;
    align-items: center;
}

.participant-avatars {
    position: relative;
    height: 26px;
    margin-right: 8px;
}

.participant-avatar {
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.participant-count {
    font-size: 12px;
    color: #999;
}

/* 投票内容 */
.poll-content {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 15px;
    margin: 15px;
    margin-bottom: 12px;
}

.poll-header {
    text-align: center;
    margin-bottom: 10px;
}

.poll-question {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.poll-options {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 12px;
}

.poll-option {
    width: 95%;
    height: 40px;
    border-radius: 5px;
    line-height: 40px;
    margin: 5px auto;
    background-color: white;
    border: 1px solid #eee;
    transition: all 0.2s;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.poll-option.selected {
    border-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.05);
}

.poll-option.view-more {
    background-color: white;
    color: #666;
}

.poll-option:active {
    transform: scale(0.99);
}

.option-text {
    font-size: 14px;
    text-align: center;
    width: 70%;
    z-index: 3;
    position: relative;
}

.option-stats {
    position: absolute;
    right: 20px;
    top: 0;
    z-index: 3;
    font-size: 12px;
    color: #666;
}

.progress-bar {
    position: absolute;
    z-index: 1;
    left: 0;
    right: 0;
    top: 0;
    width: 95%;
    height: 40px;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
    transition: width 0.3s ease;
}

.option-check {
    position: absolute;
    right: 7%;
    z-index: 3;
    top: 0;
    color: #4CAF50;
    font-weight: bold;
}

.check-icon {
    font-size: 16px;
}

.arrow-right {
    margin-left: 8px;
    font-size: 16px;
}

.poll-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
}

.poll-stats {
    font-weight: 300;
    margin-left: 23px;
    font-size: 14px;
    color: #666;
}

.vote-btn {
    font-weight: 300;
    float: right;
    margin-right: 23px;
    background-color: #e0e0e0;
    color: #666;
    border: none;
    border-radius: 20px;
    padding: 4px 16px;
    font-size: 14px;
}

/* 互动区域 */
.actions {
    display: flex;
    align-items: center;
    border-top: 1px solid #f5f5f5;
    padding-top: 8px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.action-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.action-item:active {
    transform: scale(0.95);
}

.action-icon {
    margin-right: 4px;
    transition: transform 0.2s;
    font-size: 16px;
}

.action-text {
    font-size: 14px;
}

.like-icon.liked {
    animation: like 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: #FF4D6D;
}

.bookmark-icon.bookmarked {
    color: #FFD700;
}

@keyframes like {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

.join-btn {
    background-color: #FF9800;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 16px;
    font-size: 14px;
    height: auto;
    line-height: normal;
    margin-left: auto;
    transition: background-color 0.2s;
}

.join-btn:active {
    background-color: #e68900;
}

.bookmark {
    margin-left: auto;
    margin-right: 0;
}

/* 评论预览 */
.comments-preview {
    margin: 0px 20px 20px 50px;
    padding: 15px;
    background-color: #f3f4f6;
    border-radius: 3px;
}

.comments-header {
    color: #777777;
    font-size: 12px;
    margin-bottom: 10px;
}

.comment-item {
    font-size: 14px;
    margin-top: 10px;
    line-height: 1.4;
}

.comment-username {
    color: #333;
    font-weight: 600;
    vertical-align: middle;
}

.comment-content {
    color: #666;
    display: inline-block;
    vertical-align: middle;
}

.comment-image {
    vertical-align: middle;
    height: 40px;
    width: 40px;
    border-radius: 4px;
    margin-left: 8px;
}

.view-more-comments {
    color: #2196F3;
    text-align: left;
    font-size: 12px;
    margin-top: 10px;
}

.view-more-comments:active {
    background-color: #f5f5f5;
    border-radius: 4px;
}

/* 分隔线 */
.divider {
    width: 93%;
    height: 1px;
    background-color: #f2f2f2;
    margin: 0 auto;
}

/* 加载状态 */
.cu-load {
    text-align: center;
    padding: 20px;
    font-size: 14px;
    color: #999;
}

.cu-load.loading::after {
    content: "加载中...";
}

.cu-load.over::after {
    content: "没有更多了";
}

/* 下拉刷新提示 */
.refresh-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #eee;
    border-top-color: #FF4D6D;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>