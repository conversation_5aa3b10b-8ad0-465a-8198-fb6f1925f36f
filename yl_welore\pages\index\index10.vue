<template>
    <view class="container">
        <!-- 内容列表 -->
        <view class="feed-list">
            <!-- 内容卡片 -->
            <view class="feed-card" v-for="(item, index) in feedList" :key="item.id">
                <!-- 顶部装饰条 - 不同内容类型显示不同颜色 -->
                <view class="feed-type-indicator" :class="item.type"></view>

                <!-- 用户信息 -->
                <view class="user-info">
                    <image class="avatar" :src="item.user.avatar" mode="widthFix"></image>
                    <view class="user-details">
                        <view class="username-row">
                            <text class="username">{{ item.user.name }}</text>
                            <text class="user-badge" v-if="item.user.badge">{{ item.user.badge }}</text>
                        </view>
                        <view class="post-time">
                            {{ item.time }}
                            <text class="visibility" v-if="item.visibility">· {{ item.visibility }}</text>
                        </view>
                    </view>
                    <button class="follow-btn" v-if="!item.user.followed">关注</button>
                    <button class="followed-btn" v-else>已关注</button>
                </view>

                <!-- 内容区域 - 根据类型展示不同内容 -->
                <view class="content">
                    <!-- 文本内容 -->
                    <view class="content-text" v-if="item.content.text">
                        <text v-html="item.content.text"></text>
                    </view>

                    <!-- 话题标签 -->
                    <view class="tags" v-if="item.tags && item.tags.length">
                        <text class="tag" v-for="(tag, i) in item.tags" :key="i">#{{ tag }}#</text>
                    </view>

                    <!-- 图文内容 -->
                    <view class="image-grid" v-if="item.type === 'image'">
                        <!-- 使用计算属性获取网格类名 -->
                        <view class="grid-container" :class="gridClassMap[item.id]">
                            <view class="grid-item" v-for="(img, i) in item.content.images" :key="i">
                                <image :src="img" mode="aspectFill" class="grid-img"></image>
                                <view class="img-count" v-if="item.content.images.length > 3 && i === 2">
                                    +{{ item.content.images.length - 3 }}
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 语音内容 -->
                    <view class="audio-content" v-if="item.type === 'audio'">
                        <view class="audio-player">
                            <view class="audio-icon" :class="{ playing: item.audioPlaying }">
                                <icon type="sound" size="24" color="#FF4D6D"></icon>
                            </view>
                            <view class="audio-wave" :class="{ playing: item.audioPlaying }">
                                <view class="wave-bar" :style="{ height: '15%', animationDelay: '0ms' }"></view>
                                <view class="wave-bar" :style="{ height: '40%', animationDelay: '100ms' }"></view>
                                <view class="wave-bar" :style="{ height: '25%', animationDelay: '200ms' }"></view>
                                <view class="wave-bar" :style="{ height: '50%', animationDelay: '300ms' }"></view>
                                <view class="wave-bar" :style="{ height: '30%', animationDelay: '400ms' }"></view>
                            </view>
                            <view class="audio-duration">{{ item.content.duration }}</view>
                        </view>
                        <image class="audio-cover" :src="item.content.cover" mode="aspectFill"></image>
                    </view>

                    <!-- 视频内容 -->
                    <view class="video-content" v-if="item.type === 'video'">
                        <view class="video-container">
                            <video :src="item.content.url" :poster="item.content.cover" class="video-player" controls
                                @play="handleVideoPlay(item.id)" @pause="handleVideoPause(item.id)"></video>
                            <view class="video-duration">{{ item.content.duration }}</view>
                            <view class="video-play-count">
                                <icon type="play" size="14" color="white"></icon>
                                {{ item.content.playCount }}
                            </view>
                        </view>
                    </view>

                    <!-- 活动内容 -->
                    <view class="event-content" v-if="item.type === 'event'">
                        <view class="event-banner-container">
                            <image class="event-banner" :src="item.content.banner" mode="aspectFill"></image>
                            <view class="event-tag">{{ item.content.tag }}</view>
                        </view>
                        <view class="event-info">
                            <view class="event-title">{{ item.content.title }}</view>
                            <view class="event-meta">
                                <view class="event-date">
                                    <icon type="calendar" size="16" color="#666"></icon>
                                    {{ item.content.date }}
                                </view>
                                <view class="event-location">
                                    <icon type="location" size="16" color="#666"></icon>
                                    {{ item.content.location }}
                                </view>
                            </view>
                            <view class="event-participants">
                                <view class="participant-avatars">
                                    <image v-for="(p, i) in item.content.participants" :key="i" :src="p.avatar"
                                        mode="widthFix" class="participant-avatar" :style="{ left: i * 20 + 'px' }">
                                    </image>
                                </view>
                                <view class="participant-count">
                                    已有 {{ item.content.participantCount }} 人参加
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 投票内容 -->
                    <view class="poll-content" v-if="item.type === 'poll'">
                        <view class="poll-question">{{ item.content.question }}</view>
                        <view class="poll-options">
                            <view class="poll-option" v-for="(option, i) in item.content.options" :key="i"
                                @click="selectOption(item.id, i)" :class="{ selected: item.selectedOption === i }">
                                <view class="option-text">{{ option.text }}</view>
                                <view class="option-stats">
                                    <view class="progress-bar">
                                        <view class="progress-fill"
                                            :style="{ width: option.percentage + '%', backgroundColor: getProgressColor(item.id) }">
                                        </view>
                                    </view>
                                    <view class="percentage">{{ option.percentage }}%</view>
                                </view>
                            </view>
                        </view>
                        <view class="poll-total">
                            共 {{ item.content.totalVotes }} 人参与 · 还剩 {{ item.content.daysLeft }} 天结束
                        </view>
                    </view>
                </view>

                <!-- 互动区域 -->
                <view class="actions">
                    <view class="action-item" @click="toggleLike(item.id)">
                        <icon type="heart" size="20" :color="item.liked ? getThemeColor(item.id) : '#999'"
                            :class="{ 'liked': item.liked }"></icon>
                        <text>{{ formatNumber(item.likes) }}</text>
                    </view>
                    <view class="action-item">
                        <icon type="comment" size="20" color="#999"></icon>
                        <text>{{ formatNumber(item.comments) }}</text>
                    </view>
                    <view class="action-item">
                        <icon type="share" size="20" color="#999"></icon>
                        <text>分享</text>
                    </view>
                    <view class="action-item" v-if="item.type === 'event'">
                        <button class="join-btn">参加活动</button>
                    </view>
                    <view class="action-item bookmark" @click="toggleBookmark(item.id)">
                        <icon type="star" size="20" :color="item.bookmarked ? getThemeColor(item.id) : '#999'"></icon>
                    </view>
                </view>

                <!-- 评论预览 -->
                <view class="comments-preview" v-if="item.previewComments && item.previewComments.length">
                    <view class="comment-item" v-for="(comment, i) in item.previewComments" :key="i">
                        <text class="comment-username">{{ comment.user }}：</text>
                        <text class="comment-content">{{ comment.content }}</text>
                    </view>
                    <view class="view-more-comments" v-if="item.comments > item.previewComments.length">
                        查看全部 {{ item.comments }} 条评论
                    </view>
                </view>
            </view>

            <!-- 下拉刷新提示 -->
            <view class="refresh-hint" v-if="refreshing">
                <view class="spinner"></view>
                <text>刷新中...</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            refreshing: false,
            // 直接在data中定义内容数据
            feedList: [
                {
                    id: 1,
                    type: 'image',
                    themeColor: '#FF4D6D', // 粉色 - 图文类型
                    user: {
                        avatar: 'https://picsum.photos/id/64/200/200',
                        name: '夏日限定',
                        badge: '旅行达人',
                        followed: false
                    },
                    time: '2小时前',
                    visibility: '公开',
                    content: {
                        text: '周末去了郊外露营，天气超级好！分享一些美图给大家～ 你们周末都去哪里玩了呀？',
                        images: [
                            'https://picsum.photos/id/10/600/400',
                            'https://picsum.photos/id/11/600/400',
                            'https://picsum.photos/id/12/600/400',
                            'https://picsum.photos/id/13/600/400'
                        ]
                    },
                    tags: ['露营', '周末去哪儿', '自然风光'],
                    likes: 245,
                    comments: 36,
                    liked: false,
                    bookmarked: true,
                    previewComments: [
                        { user: '旅行爱好者', content: '这地方太美了！请问具体在哪里呀？' },
                        { user: '户外达人', content: '装备看起来很专业哦～' }
                    ]
                },
                {
                    id: 2,
                    type: 'audio',
                    themeColor: '#7B61FF', // 紫色 - 语音类型
                    user: {
                        avatar: 'https://picsum.photos/id/65/200/200',
                        name: '音乐旅人',
                        badge: '原创音乐人',
                        followed: true
                    },
                    time: '4小时前',
                    visibility: '好友可见',
                    content: {
                        text: '新写的原创歌曲片段，希望大家喜欢～ 评论区告诉我你们的感受吧！',
                        cover: 'https://picsum.photos/id/20/300/300',
                        duration: '01:24'
                    },
                    tags: ['原创音乐', '流行', '创作日常'],
                    audioPlaying: false,
                    likes: 189,
                    comments: 52,
                    liked: true,
                    bookmarked: false,
                    previewComments: [
                        { user: '音乐迷', content: '旋律好好听！期待完整版～' }
                    ]
                },
                {
                    id: 3,
                    type: 'video',
                    themeColor: '#00C853', // 绿色 - 视频类型
                    user: {
                        avatar: 'https://picsum.photos/id/66/200/200',
                        name: '舞蹈达人',
                        badge: '舞蹈老师',
                        followed: false
                    },
                    time: '昨天',
                    visibility: '公开',
                    content: {
                        text: '最新编舞视频，练习了好久终于完美呈现！求点赞鼓励～',
                        url: '', // 实际项目中填入视频地址
                        cover: 'https://picsum.photos/id/30/600/400',
                        duration: '00:58',
                        playCount: '1.2k'
                    },
                    tags: ['舞蹈', '编舞', '流行舞'],
                    likes: 567,
                    comments: 128,
                    liked: false,
                    bookmarked: false,
                    previewComments: [
                        { user: '舞者小李', content: '动作好流畅！卡点太舒服了' },
                        { user: '舞蹈爱好者', content: '求教程！想学这个舞' }
                    ]
                },
                {
                    id: 4,
                    type: 'event',
                    themeColor: '#FF9800', // 橙色 - 活动类型
                    user: {
                        avatar: 'https://picsum.photos/id/67/200/200',
                        name: '城市活动家',
                        badge: '活动组织者',
                        followed: false
                    },
                    time: '3天前',
                    visibility: '公开',
                    content: {
                        title: '城市青年音乐节',
                        tag: '音乐',
                        banner: 'https://picsum.photos/id/40/600/300',
                        date: '6月15日 19:00',
                        location: '中央公园',
                        participants: [
                            { avatar: 'https://picsum.photos/id/91/100/100' },
                            { avatar: 'https://picsum.photos/id/92/100/100' },
                            { avatar: 'https://picsum.photos/id/93/100/100' }
                        ],
                        participantCount: 156
                    },
                    tags: ['音乐节', '户外活动', '青年社交'],
                    likes: 98,
                    comments: 24,
                    liked: false,
                    bookmarked: true
                },
                {
                    id: 5,
                    type: 'poll',
                    themeColor: '#2196F3', // 蓝色 - 投票类型
                    user: {
                        avatar: 'https://picsum.photos/id/68/200/200',
                        name: '潮流先锋',
                        badge: '时尚博主',
                        followed: true
                    },
                    time: '1周前',
                    visibility: '公开',
                    content: {
                        question: '夏天最适合的穿搭风格是？',
                        options: [
                            { text: '休闲运动风', percentage: 45 },
                            { text: '甜美可爱风', percentage: 30 },
                            { text: '简约冷淡风', percentage: 25 }
                        ],
                        totalVotes: 328,
                        daysLeft: 3
                    },
                    tags: ['时尚', '穿搭', '夏季'],
                    selectedOption: null,
                    likes: 67,
                    comments: 42,
                    liked: false,
                    bookmarked: false,
                    previewComments: [
                        { user: '时尚达人', content: '必须是休闲运动风！舒适又好看' },
                        { user: '穿搭控', content: '甜美风yyds！夏天就该活力满满' }
                    ]
                }
            ]
        };
    },
    computed: {
        // 计算每个图文内容的网格布局类名，存储在对象中
        gridClassMap() {
            const map = {};
            this.feedList.forEach(item => {
                if (item.type === 'image' && item.content.images) {
                    const count = item.content.images.length;
                    if (count === 1) {
                        map[item.id] = 'grid-single';
                    } else if (count === 2) {
                        map[item.id] = 'grid-double';
                    } else {
                        map[item.id] = 'grid-multiple';
                    }
                }
            });
            return map;
        }
    },
    methods: {
        // 点赞/取消点赞
        toggleLike(id) {
            const item = this.feedList.find(item => item.id === id);
            if (item) {
                if (item.liked) {
                    item.likes--;
                } else {
                    item.likes++;
                }
                item.liked = !item.liked;
            }
        },

        // 收藏/取消收藏
        toggleBookmark(id) {
            const item = this.feedList.find(item => item.id === id);
            if (item) {
                item.bookmarked = !item.bookmarked;
            }
        },

        // 选择投票选项
        selectOption(id, optionIndex) {
            const item = this.feedList.find(item => item.id === id);
            if (item) {
                // 如果已经选择了该选项，则取消
                if (item.selectedOption === optionIndex) {
                    item.selectedOption = null;
                } else {
                    item.selectedOption = optionIndex;
                }
            }
        },

        // 处理视频播放
        handleVideoPlay(id) {
            // 暂停其他正在播放的视频
            this.feedList.forEach(item => {
                if (item.id !== id && item.type === 'video') {
                    // 实际项目中可以通过video上下文暂停视频
                }
            });
        },

        // 处理视频暂停
        handleVideoPause(id) {
            // 视频暂停处理
        },

        // 下拉刷新
        onRefresh() {
            this.refreshing = true;
            // 模拟刷新延迟
            setTimeout(() => {
                this.refreshing = false;
                // 实际项目中可以在这里重新请求数据
            }, 1000);
        },

        // 格式化数字（大于1000显示k）
        formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num;
        },

        // 获取项目主题色
        getThemeColor(id) {
            const item = this.feedList.find(item => item.id === id);
            return item ? item.themeColor : '#FF4D6D';
        },

        // 获取进度条颜色
        getProgressColor(id) {
            const item = this.feedList.find(item => item.id === id);
            return item ? item.themeColor : '#2196F3';
        }
    }
};
</script>

<style scoped>
/* 样式保持不变，与上一版本相同 */
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 内容列表 */
.feed-list {
    flex: 1;
    padding: 12px 0;
}

/* 内容卡片 */
.feed-card {
    background-color: white;
    border-radius: 12px;
    margin: 0 16px 12px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.feed-card:active {
    transform: scale(0.995);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
}

/* 内容类型指示条 */
.feed-type-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    border-radius: 2px 0 0 2px;
}

.feed-type-indicator.image {
    background-color: #FF4D6D;
}

.feed-type-indicator.audio {
    background-color: #7B61FF;
}

.feed-type-indicator.video {
    background-color: #00C853;
}

.feed-type-indicator.event {
    background-color: #FF9800;
}

.feed-type-indicator.poll {
    background-color: #2196F3;
}

/* 其他样式保持不变 */
.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #f0f0f0;
}

.user-details {
    flex: 1;
}

.username-row {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.username {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-right: 6px;
}

.user-badge {
    font-size: 11px;
    background-color: #f0f0f0;
    color: #666;
    padding: 1px 6px;
    border-radius: 10px;
}

.post-time {
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
}

.visibility {
    font-size: 11px;
}

.follow-btn {
    background-color: #FF4D6D;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
    transition: background-color 0.2s;
}

.follow-btn:active {
    background-color: #e0435f;
}

.followed-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 3px 12px;
    font-size: 13px;
    height: auto;
    line-height: normal;
}

/* 内容区域 */
.content {
    margin-bottom: 16px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.content-text {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 12px;
    white-space: pre-line;
}

/* 话题标签 */
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.tag {
    font-size: 13px;
    color: #6B7280;
    background-color: #F3F4F6;
    padding: 2px 8px;
    border-radius: 4px;
}

/* 图文内容 */
.image-grid {
    margin-bottom: 12px;
}

.grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.grid-single {
    aspect-ratio: 16/9;
}

.grid-single .grid-item {
    width: 100%;
    height: 100%;
}

.grid-double .grid-item {
    width: calc(50% - 2px);
    aspect-ratio: 1;
}

.grid-multiple .grid-item {
    width: calc(33.333% - 2.66px);
    aspect-ratio: 1;
}

.grid-item {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.grid-img {
    width: 100%;
    height: 100%;
    display: block;
}

.img-count {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px 0 0 0;
}

/* 语音内容 */
.audio-content {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.audio-player {
    flex: 1;
    display: flex;
    align-items: center;
}

.audio-icon {
    margin-right: 12px;
    transition: transform 0.3s;
}

.audio-icon.playing {
    transform: scale(1.1);
}

.audio-wave {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 30px;
    margin-right: 12px;
}

.audio-wave.playing .wave-bar {
    animation: wave 1s infinite ease-in-out;
}

.wave-bar {
    width: 3px;
    background-color: #7B61FF;
    border-radius: 3px;
    transform-origin: center bottom;
}

@keyframes wave {

    0%,
    100% {
        transform: scaleY(0.5);
    }

    50% {
        transform: scaleY(1);
    }
}

.audio-duration {
    font-size: 12px;
    color: #999;
}

.audio-cover {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-left: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 视频内容 */
.video-content {
    margin-bottom: 12px;
}

.video-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background-color: #000;
}

.video-player {
    width: 100%;
    aspect-ratio: 16/9;
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 10;
}

.video-play-count {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 10;
}

/* 活动内容 */
.event-content {
    margin-bottom: 12px;
}

.event-banner-container {
    position: relative;
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
}

.event-banner {
    width: 100%;
    aspect-ratio: 2/1;
    display: block;
}

.event-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #FF9800;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
}

.event-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.event-date,
.event-location {
    display: flex;
    align-items: center;
    gap: 4px;
}

.event-participants {
    display: flex;
    align-items: center;
}

.participant-avatars {
    position: relative;
    height: 26px;
    margin-right: 8px;
}

.participant-avatar {
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.participant-count {
    font-size: 12px;
    color: #999;
}

/* 投票内容 */
.poll-content {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
}

.poll-question {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.4;
}

.poll-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.poll-option {
    background-color: white;
    border-radius: 6px;
    padding: 14px;
    border: 1px solid #eee;
    transition: all 0.2s;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.poll-option.selected {
    border-color: #2196F3;
    background-color: rgba(33, 150, 243, 0.05);
}

.poll-option:active {
    transform: scale(0.99);
}

.option-text {
    font-size: 14px;
    margin-bottom: 8px;
}

.option-stats {
    display: flex;
    align-items: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: #eee;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.percentage {
    font-size: 12px;
    color: #666;
    margin-left: 8px;
    width: 40px;
    text-align: right;
    font-weight: 500;
}

.poll-total {
    font-size: 12px;
    color: #999;
    text-align: right;
    padding-top: 4px;
}

/* 互动区域 */
.actions {
    display: flex;
    align-items: center;
    border-top: 1px solid #f5f5f5;
    padding-top: 8px;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.action-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.action-item:active {
    transform: scale(0.95);
}

.action-item icon {
    margin-right: 4px;
    transition: transform 0.2s;
}

.action-item .liked {
    animation: like 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes like {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}

.join-btn {
    background-color: #FF9800;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 4px 16px;
    font-size: 14px;
    height: auto;
    line-height: normal;
    margin-left: auto;
    transition: background-color 0.2s;
}

.join-btn:active {
    background-color: #e68900;
}

.bookmark {
    margin-left: auto;
    margin-right: 0;
}

/* 评论预览 */
.comments-preview {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed #f0f0f0;
    padding-left: 8px;
    /* 为类型指示条留出空间 */
}

.comment-item {
    font-size: 14px;
    margin-bottom: 6px;
    line-height: 1.4;
}

.comment-username {
    color: #333;
    font-weight: 500;
}

.comment-content {
    color: #666;
}

.view-more-comments {
    font-size: 13px;
    color: #6B7280;
    margin-top: 4px;
    padding: 4px 0;
}

.view-more-comments:active {
    background-color: #f5f5f5;
    border-radius: 4px;
}

/* 下拉刷新提示 */
.refresh-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #eee;
    border-top-color: #FF4D6D;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>